#ifndef SCENE_H
#define SCENE_H

#include <QTimer>
#include <QGraphicsScene>
#include <birditem.h>
#include "pipeitem.h"
#include "ground.h"
#include <QGraphicsPixmapItem>

class Scene : public QGraphicsScene
{
    Q_OBJECT
public:
    explicit Scene(QObject *parent = nullptr);
    void birddef();
    void mainstart();
    void Scoreadd();
    void restart();

private:
    void setpipetimer();
    QTimer* pipetimer;
    birditem* bird;
    QGraphicsPixmapItem* startImage;
    QGraphicsPixmapItem* startLogoImage;
    QGraphicsPixmapItem* gameoverImage;
    QGraphicsPixmapItem* zangdaImg;
    bool startsign;
    void gameover();
    groundItem* ground;
    bool gameoverbool;
    int score;
    QGraphicsTextItem* scoretext;
    void showscore();

protected:
    void keyPressEvent(QKeyEvent *event);
};

#endif
