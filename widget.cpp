#include "widget.h"
#include "ui_widget.h"
#include <QGraphicsPixmapItem>
#include <QIcon>

Widget::Widget(QWidget *parent)
    : QWidget(parent)
    , ui(new Ui::Widget)
{
    ui->setupUi(this);

    setWindowTitle("FlappyBird");
    setWindowIcon(QIcon(":/new/prefix1/logo.ico"));

    scene = new Scene(this);

    scene->setSceneRect(0,0,432,644);

    QGraphicsPixmapItem* pixItem = new QGraphicsPixmapItem(QPixmap(":/new/prefix1/bg.png"));
    scene->addItem(pixItem);
    pixItem->setPos(0,0);

    scene->birddef();

    ui->Box->setScene(scene);
}
Widget::~Widget()
{
    delete ui;
}