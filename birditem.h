#ifndef BIRDITEM_H
#define BIRDITEM_H

#include <QObject>
#include <QGraphicsPixmapItem>
#include <QPropertyAnimation>

class birditem : public QObject , public QGraphicsPixmapItem
{
    Q_OBJECT
    Q_PROPERTY(qreal y READ y WRITE sety)
    Q_PROPERTY(qreal rotation READ rotation WRITE setRotation)
public:
    explicit birditem(QPixmap pixmap,QObject *parent = nullptr);
    void wings();
    qreal y() const;
    qreal rotation() const;
    void jump();

    void birdstart();

    void birdstop();

    bool collision();

public slots:
    void sety(qreal y);

    void setRotation(qreal rotation);
    void falling();

private:
    bool wingdirect;
    int wingpos;
    QPropertyAnimation* yani;
    QPropertyAnimation* rotationani;
    qreal m_y;
    qreal m_rotation;
    qreal groundline;

signals:
    void collidesignal2();
};

#endif
