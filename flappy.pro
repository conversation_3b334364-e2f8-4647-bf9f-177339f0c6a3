QT       += core gui

greaterThan(QT_MAJOR_VERSION, 4): QT += widgets

CONFIG += c++11

DEFINES += QT_DEPRECATED_WARNINGS

SOURCES += \
    birditem.cpp \
    ground.cpp \
    main.cpp \
    pipeitem.cpp \
    scene.cpp \
    widget.cpp

HEADERS += \
    birditem.h \
    ground.h \
    pipeitem.h \
    scene.h \
    widget.h

FORMS += \
    widget.ui

qnx: target.path = /tmp/$${TARGET}/bin
else: unix:!android: target.path = /opt/$${TARGET}/bin
!isEmpty(target.path): INSTALLS += target

RESOURCES += \
    resources.qrc

RC_ICONS = pics/logo.ico
