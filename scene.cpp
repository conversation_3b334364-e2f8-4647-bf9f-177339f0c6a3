#include "scene.h"
#include <QKeyEvent>
#include <birditem.h>
#include "ground.h"

Scene::Scene(QObject *parent) : QGraphicsScene(parent),startsign(0),gameoverbool(0),score(0)
{
    setpipetimer();

    startImage = new QGraphicsPixmapItem(QPixmap(":/new/prefix1/start.png"));
    addItem(startImage);
    startImage->setPos(0,0);
    startImage->setZValue(100);

    QPixmap startLogoPixmap(":/new/prefix1/zangda_main.png");
    QPixmap scaledStartLogo = startLogoPixmap.scaled(200, 150, Qt::KeepAspectRatio, Qt::SmoothTransformation);
    startLogoImage = new QGraphicsPixmapItem(scaledStartLogo);
    addItem(startLogoImage);
    qreal logoX = (432 - startLogoImage->boundingRect().width()) / 2;
    startLogoImage->setPos(logoX, 450);
    startLogoImage->setZValue(99);

    gameoverImage = new QGraphicsPixmapItem(QPixmap(":/new/prefix1/gameover.png"));
    QPixmap originalPixmap(":/new/prefix1/zangda.png");
    QPixmap scaledPixmap = originalPixmap.scaled(150, 100, Qt::KeepAspectRatio, Qt::SmoothTransformation);
    nankaiImage = new QGraphicsPixmapItem(scaledPixmap);

    ground = new groundItem;
    addItem(ground);
    ground->setZValue(10);
}

void Scene::birddef()
{
    bird = new birditem(QPixmap(":/new/prefix1/0.png"));
    addItem(bird);
    bird->setZValue(20);
}

void Scene::mainstart()
{
    startsign=1;
    removeItem(startImage);
    delete startImage;
    removeItem(startLogoImage);
    delete startLogoImage;
    bird->birdstart();
    if(!pipetimer->isActive()){
        pipetimer->start(2000);
    }
}

void Scene::Scoreadd()
{
    score++;
}

void Scene::setpipetimer()
{
 pipetimer = new QTimer(this);
 connect(pipetimer,&QTimer::timeout,[=](){
     PipeItem* pipe = new PipeItem;

     connect(pipe,&PipeItem::collidesignal,[=](){
         pipetimer->stop();
         gameover();
     });
     connect(bird,&birditem::collidesignal2,[=](){
         gameover();
     });
     addItem(pipe);
 });
}

void Scene::gameover()
{
    gameoverbool=1;
    bird->birdstop();
    ground->groundstop();
    showscore();

    addItem(gameoverImage);
    gameoverImage->setPos(0,0);
    gameoverImage->setZValue(100);
    addItem(nankaiImage);
    qreal centerX = (432 - nankaiImage->boundingRect().width()) / 2;
    nankaiImage->setPos(centerX, 450);
    nankaiImage->setZValue(100);

    QList<QGraphicsItem*> sceneItems = items();
    for(int i=0; i<sceneItems.size(); i++){
        PipeItem * pipe = qgraphicsitem_cast<PipeItem*>(sceneItems[i]);
        if(pipe){
            pipe->pipestop();
        }
    }
    pipetimer->stop();
}

void Scene::showscore()
{
    if(scoretext && scoretext->scene()) {
        removeItem(scoretext);
        delete scoretext;
        scoretext = nullptr;
    }

    scoretext = new QGraphicsTextItem();
    QString lastscore="分数：" + QString::number(score);
    scoretext->setHtml(lastscore);

    QFont font("Consolas",20,QFont::Bold);
    scoretext->setFont(font);
    QColor color(126,12,110);
    scoretext->setDefaultTextColor(color);

    addItem(scoretext);
    qreal textCenterX = (432 - scoretext->boundingRect().width()) / 2;
    scoretext->setPos(textCenterX, 280);
}

void Scene::keyPressEvent(QKeyEvent *event)
{
    if(event->key() == Qt::Key_Space) {
        if(gameoverbool) {
            restart();
        }
        else if(startsign == 0) {
            mainstart();
        }
        else {
            bird->jump();
        }
    }
    QGraphicsScene::keyPressEvent(event);
}

void Scene::restart()
{
    if(gameoverImage && gameoverImage->scene()) {
        removeItem(gameoverImage);
        delete gameoverImage;
        gameoverImage = nullptr;
    }
    if(nankaiImage && nankaiImage->scene()) {
        removeItem(nankaiImage);
        delete nankaiImage;
        nankaiImage = nullptr;
    }
    if(scoretext && scoretext->scene()) {
        removeItem(scoretext);
        delete scoretext;
        scoretext = nullptr;
    }

    QList<QGraphicsItem*> sceneItems = items();
    for(int i = sceneItems.size()-1; i >= 0; i--) {
        PipeItem* pipe = qgraphicsitem_cast<PipeItem*>(sceneItems[i]);
        if(pipe) {
            removeItem(pipe);
            delete pipe;
        }
    }

    gameoverbool = false;
    startsign = false;
    score = 0;
    scoretext = nullptr;

    gameoverImage = new QGraphicsPixmapItem(QPixmap(":/new/prefix1/gameover.png"));
    QPixmap originalPixmap(":/new/prefix1/zangda.png");
    QPixmap scaledPixmap = originalPixmap.scaled(150, 100, Qt::KeepAspectRatio, Qt::SmoothTransformation);
    nankaiImage = new QGraphicsPixmapItem(scaledPixmap);

    startImage = new QGraphicsPixmapItem(QPixmap(":/new/prefix1/start.png"));
    addItem(startImage);
    startImage->setPos(0,0);
    startImage->setZValue(100);

    QPixmap startLogoPixmap(":/new/prefix1/zangda_main.png");
    QPixmap scaledStartLogo = startLogoPixmap.scaled(200, 150, Qt::KeepAspectRatio, Qt::SmoothTransformation);
    startLogoImage = new QGraphicsPixmapItem(scaledStartLogo);
    addItem(startLogoImage);
    qreal logoX = (432 - startLogoImage->boundingRect().width()) / 2;
    startLogoImage->setPos(logoX, 450);
    startLogoImage->setZValue(99);

    if(bird && bird->scene()) {
        removeItem(bird);
        delete bird;
    }
    birddef();

    ground->groundani->start();

    if(pipetimer->isActive()) {
        pipetimer->stop();
    }
}
